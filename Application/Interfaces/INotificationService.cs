using Application.DTOs;

namespace Application.Interfaces;

public interface INotificationService
{
    Task<IEnumerable<NotificationDto>> GetForUserAsync(string userId);
    Task<NotificationDto?> GetByIdAsync(Guid id);
    Task<NotificationDto?> CreateAsync(CreateNotificationRequest request);
    Task<bool> MarkAsReadAsync(Guid id);
    Task<int> MarkAllAsReadAsync(string userId);
    Task<bool> DeleteAsync(Guid id);
}
