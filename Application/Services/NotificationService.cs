using Application.DTOs;
using Application.Interfaces;
using Persistence.Entities;
using Persistence.Interfaces;

namespace Application.Services;

public class NotificationService(INotificationRepository notificationRepository) : INotificationService
{
    private readonly INotificationRepository _notificationRepository = notificationRepository;

    public async Task<IEnumerable<NotificationDto>> GetForUserAsync(string userId)
    {
        var result = await _notificationRepository.GetByUserIdAsync(userId);
        if (!result.IsSuccess || result.Data == null)
        {
            return Enumerable.Empty<NotificationDto>();
        }

        return result.Data.Select(MapToDto);
    }

    public async Task<NotificationDto?> GetByIdAsync(Guid id)
    {
        var result = await _notificationRepository.GetByIdAsync(id);
        return result.IsSuccess && result.Data != null ? MapToDto(result.Data) : null;
    }

    public async Task<NotificationDto?> CreateAsync(CreateNotificationRequest request)
    {
        var entity = new NotificationEntity
        {
            Id = Guid.NewGuid(),
            UserId = request.UserId,
            Message = request.Message,
            Type = request.Type,
            CreatedAt = DateTime.UtcNow,
            IsRead = false
        };

        var result = await _notificationRepository.AddAsync(entity);
        return result.IsSuccess && result.Data != null ? MapToDto(result.Data) : null;
    }

    public async Task<bool> MarkAsReadAsync(Guid id)
    {
        var result = await _notificationRepository.MarkAsReadAsync(id);
        return result.IsSuccess && result.Data;
    }

    public async Task<int> MarkAllAsReadAsync(string userId)
    {
        var result = await _notificationRepository.MarkAllAsReadAsync(userId);
        return result.IsSuccess && result.Data != null ? result.Data : 0;
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        var result = await _notificationRepository.DeleteAsync(id);
        return result.IsSuccess && result.Data;
    }

    private static NotificationDto MapToDto(NotificationEntity entity)
    {
        return new NotificationDto
        {
            Id = entity.Id,
            UserId = entity.UserId,
            Message = entity.Message,
            Type = entity.Type,
            CreatedAt = entity.CreatedAt,
            IsRead = entity.IsRead,
            ReadAt = entity.ReadAt
        };
    }
}
