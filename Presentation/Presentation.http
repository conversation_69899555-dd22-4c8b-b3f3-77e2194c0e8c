@Presentation_HostAddress = http://localhost:5001

### Get notifications for a user
GET {{Presentation_HostAddress}}/api/notifications/user/demo-user
Accept: application/json

### Create a notification
POST {{Presentation_HostAddress}}/api/notifications
Content-Type: application/json

{
  "userId": "demo-user",
  "message": "Welcome to the platform!",
  "type": "info"
}

### Mark a notification as read
POST {{Presentation_HostAddress}}/api/notifications/00000000-0000-0000-0000-000000000000/read

### Delete a notification
DELETE {{Presentation_HostAddress}}/api/notifications/00000000-0000-0000-0000-000000000000
