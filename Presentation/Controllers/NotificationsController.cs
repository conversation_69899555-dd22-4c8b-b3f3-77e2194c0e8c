using Application.DTOs;
using Application.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Presentation.Controllers;

[ApiController]
[Route("api/[controller]")]
public class NotificationsController(INotificationService notificationService) : ControllerBase
{
    private readonly INotificationService _notificationService = notificationService;

    [HttpGet("user/{userId}")]
    public async Task<ActionResult<IEnumerable<NotificationDto>>> GetForUser(string userId)
    {
        var notifications = await _notificationService.GetForUserAsync(userId);
        return Ok(notifications);
    }

    [HttpGet("{id:guid}")]
    public async Task<ActionResult<NotificationDto>> GetById(Guid id)
    {
        var notification = await _notificationService.GetByIdAsync(id);
        return notification != null ? Ok(notification) : NotFound();
    }

    [HttpPost]
    public async Task<ActionResult<NotificationDto>> Create(CreateNotificationRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var created = await _notificationService.CreateAsync(request);
        if (created == null)
        {
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to create notification");
        }

        return CreatedAtAction(nameof(GetById), new { id = created.Id }, created);
    }

    [HttpPost("{id:guid}/read")]
    public async Task<IActionResult> MarkAsRead(Guid id)
    {
        var success = await _notificationService.MarkAsReadAsync(id);
        return success ? NoContent() : NotFound();
    }

    [HttpPost("user/{userId}/read")]
    public async Task<IActionResult> MarkAllAsRead(string userId)
    {
        var updated = await _notificationService.MarkAllAsReadAsync(userId);
        return Ok(new { updated });
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        var success = await _notificationService.DeleteAsync(id);
        return success ? NoContent() : NotFound();
    }
}
