using Microsoft.EntityFrameworkCore;
using Persistence.Contexts;
using Persistence.Entities;
using Persistence.Interfaces;
using Persistence.Models;

namespace Persistence.Repositories;

public class NotificationRepository : BaseRepository<NotificationEntity>, INotificationRepository
{
    public NotificationRepository(DataContext context) : base(context)
    {
    }

    public async Task<RepositoryResult<IEnumerable<NotificationEntity>>> GetByUserIdAsync(string userId)
    {
        try
        {
            var notifications = await DbSet
                .Where(notification => notification.UserId == userId)
                .OrderByDescending(notification => notification.CreatedAt)
                .ToListAsync();

            return RepositoryResult<IEnumerable<NotificationEntity>>.Success(notifications);
        }
        catch (Exception ex)
        {
            return RepositoryResult<IEnumerable<NotificationEntity>>.Failure(
                $"Error retrieving notifications: {ex.Message}"
            );
        }
    }

    public async Task<RepositoryResult<bool>> MarkAsReadAsync(Guid id)
    {
        try
        {
            var notification = await DbSet.FindAsync(id);
            if (notification == null)
            {
                return RepositoryResult<bool>.Failure("Notification not found");
            }

            if (!notification.IsRead)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.UtcNow;
                Context.Entry(notification).State = EntityState.Modified;
                await Context.SaveChangesAsync();
            }

            return RepositoryResult<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return RepositoryResult<bool>.Failure($"Error updating notification: {ex.Message}");
        }
    }

    public async Task<RepositoryResult<int>> MarkAllAsReadAsync(string userId)
    {
        try
        {
            var notifications = await DbSet
                .Where(notification => notification.UserId == userId && !notification.IsRead)
                .ToListAsync();

            if (notifications.Count == 0)
            {
                return RepositoryResult<int>.Success(0);
            }

            foreach (var notification in notifications)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.UtcNow;
            }

            await Context.SaveChangesAsync();
            return RepositoryResult<int>.Success(notifications.Count);
        }
        catch (Exception ex)
        {
            return RepositoryResult<int>.Failure($"Error updating notifications: {ex.Message}");
        }
    }
}
