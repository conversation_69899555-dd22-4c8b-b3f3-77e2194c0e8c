using Microsoft.EntityFrameworkCore;
using Persistence.Contexts;
using Persistence.Interfaces;
using Persistence.Models;

namespace Persistence.Repositories;

public class BaseRepository<TEntity> : IBaseRepository<TEntity>
    where TEntity : class
{
    protected readonly DataContext Context;
    protected readonly DbSet<TEntity> DbSet;

    public BaseRepository(DataContext context)
    {
        Context = context;
        DbSet = context.Set<TEntity>();
    }

    public virtual async Task<RepositoryResult<TEntity>> GetByIdAsync(Guid id)
    {
        try
        {
            var entity = await DbSet.FindAsync(id);
            return entity != null
                ? RepositoryResult<TEntity>.Success(entity)
                : RepositoryResult<TEntity>.Failure("Entity not found");
        }
        catch (Exception ex)
        {
            return RepositoryResult<TEntity>.Failure($"Error retrieving entity: {ex.Message}");
        }
    }

    public virtual async Task<RepositoryResult<IEnumerable<TEntity>>> GetAllAsync()
    {
        try
        {
            var entities = await DbSet.ToListAsync();
            return RepositoryResult<IEnumerable<TEntity>>.Success(entities);
        }
        catch (Exception ex)
        {
            return RepositoryResult<IEnumerable<TEntity>>.Failure($"Error retrieving entities: {ex.Message}");
        }
    }

    public virtual async Task<RepositoryResult<TEntity>> AddAsync(TEntity entity)
    {
        try
        {
            await DbSet.AddAsync(entity);
            await Context.SaveChangesAsync();
            return RepositoryResult<TEntity>.Success(entity);
        }
        catch (Exception ex)
        {
            return RepositoryResult<TEntity>.Failure($"Error adding entity: {ex.Message}");
        }
    }

    public virtual async Task<RepositoryResult<TEntity>> UpdateAsync(TEntity entity)
    {
        try
        {
            DbSet.Update(entity);
            await Context.SaveChangesAsync();
            return RepositoryResult<TEntity>.Success(entity);
        }
        catch (Exception ex)
        {
            return RepositoryResult<TEntity>.Failure($"Error updating entity: {ex.Message}");
        }
    }

    public virtual async Task<RepositoryResult<bool>> DeleteAsync(Guid id)
    {
        try
        {
            var entity = await DbSet.FindAsync(id);
            if (entity == null)
            {
                return RepositoryResult<bool>.Failure("Entity not found");
            }

            DbSet.Remove(entity);
            await Context.SaveChangesAsync();
            return RepositoryResult<bool>.Success(true);
        }
        catch (Exception ex)
        {
            return RepositoryResult<bool>.Failure($"Error deleting entity: {ex.Message}");
        }
    }
}
