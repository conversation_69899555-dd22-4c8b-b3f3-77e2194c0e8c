using Microsoft.EntityFrameworkCore;
using Persistence.Entities;

namespace Persistence.Contexts;

public class DataContext(DbContextOptions<DataContext> options) : DbContext(options)
{
    public DbSet<NotificationEntity> Notifications => Set<NotificationEntity>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<NotificationEntity>(entity =>
        {
            entity.ToTable("Notifications");
            entity.HasKey(n => n.Id);

            entity.Property(n => n.UserId)
                .IsRequired()
                .HasMaxLength(64);

            entity.Property(n => n.Message)
                .IsRequired()
                .HasMaxLength(512);

            entity.Property(n => n.Type)
                .HasMaxLength(64);

            entity.Property(n => n.CreatedAt)
                .HasDefaultValueSql("GETUTCDATE()");
        });
    }
}
